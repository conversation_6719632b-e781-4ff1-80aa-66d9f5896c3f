import { useMutation, useQueryClient } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	DeleteHolidayResponse,
} from "@/shared/types/api";
import { deleteHoliday } from "../api/deleteHoliday";

interface UseDeleteHolidayOptions {
	onSuccessCallback?: (data: DeleteHolidayResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useDeleteHoliday = (options?: UseDeleteHolidayOptions) => {
	const queryClient = useQueryClient();

	return useMutation<
		DeleteHolidayResponse,
		AxiosError<BaseErrorResponse>,
		string
	>({
		mutationFn: deleteHoliday,
		onSuccess: async (data) => {
			// queryClient.invalidateQueries({ queryKey: ['todos'] })
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
