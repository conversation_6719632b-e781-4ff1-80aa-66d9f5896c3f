import { <PERSON>, Button, Chip, Stack, Typography } from "@mui/material";
import type { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { Link as ReactRouterLink, useNavigate } from "react-router";
import IconsaxAddSquareIcon from "@/assets/icons/iconsax-add-square.svg?react";
import IconsaxReceiveSquareIcon from "@/assets/icons/iconsax-receive-square.svg?react";
import { DataTable } from "@/shared/components/common/DataTable";
import { ColumnHeader } from "@/shared/components/common/DataTable/ColumnHeader";
import { RowActions } from "@/shared/components/common/DataTable/RowActions";
import { ViewOptions } from "@/shared/components/common/DataTable/ViewOption";
import { Footer } from "@/shared/components/common/Footer";
import { IconWrapper } from "@/shared/components/common/IconWrapper";
import { useBulkActionHandler } from "@/shared/hooks/useBulkActionHandler";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { LeaveRequestResponse } from "@/shared/types/api";
import type { SelectionData } from "@/shared/types/common";
import { getAllLeaveRequest } from "../api/getAllLeaveRequest";
import {
	LeaveRequestStatus,
	LeaveRequestStatusLabel,
	leaveRequestStatusSelectOption,
} from "../constants";
import { useBulkActionLeaveRequest } from "../hooks/useBulkActionLeaveRequest";

const LeaveRequestListPage: React.FC = () => {
	const navigate = useNavigate();
	const [selectionData, setSelectionData] = useState<SelectionData>({
		selectedIds: [],
		excludedIds: [],
		isSelectAll: false,
		selectionInfo: { selectedCount: 0, totalCount: 0, isSelectAll: false },
	});

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useBulkActionLeaveRequest({
		onSuccessCallback: (data) => {
			if (data instanceof Blob) {
				// Download CSV
				const url = window.URL.createObjectURL(data);
				const a = document.createElement("a");
				a.href = url;
				a.download = "exported_leave_requests.csv";
				document.body.appendChild(a);
				a.click();
				a.remove();
				window.URL.revokeObjectURL(url);
			} else {
				handleSuccess(data.message);
			}
		},
		onErrorCallback: handleError,
	});

	const handleBulkAction = useBulkActionHandler(mutation, selectionData);

	const columns: ColumnDef<LeaveRequestResponse>[] = [
		{
			accessorKey: "userName",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Nama Kariawan" />
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Nama Kariawan", filterVariant: "textSearch" },
		},
		{
			accessorKey: "userEmail",
			header: ({ column }) => <ColumnHeader column={column} title="Email" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Email", filterVariant: "textSearch" },
		},
		{
			accessorKey: "name",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Jenis Cuti" />
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Jenis Cuti", filterVariant: "textSearch" },
		},
		{
			accessorKey: "reason",
			header: ({ column }) => (
				<ColumnHeader
					sx={{ minWidth: 200 }}
					column={column}
					title="Perihal Cuti"
				/>
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: false,
			enableSorting: false,
			meta: { columnLabel: "Perihal Cuti" },
		},
		{
			accessorKey: "status",
			header: ({ column }) => <ColumnHeader column={column} title="Status" />,
			cell: (info) => {
				const status = info.getValue();

				return (
					<Chip
						label={
							LeaveRequestStatusLabel[
								status as keyof typeof LeaveRequestStatusLabel
							]
						}
						color={
							status === LeaveRequestStatus.PENDING
								? "warning"
								: status === LeaveRequestStatus.APPROVED
									? "success"
									: "error"
						}
						size="small"
					/>
				);
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: {
				columnLabel: "Status",
				filterVariant: "select",
				selectOptions: leaveRequestStatusSelectOption,
			},
		},
		{
			accessorKey: "startDate",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Tanggal Mulai" />
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Tanggal Mulai", filterVariant: "dateRange" },
		},
		{
			accessorKey: "endDate",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Tanggal Selesai" />
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Tanggal Selesai", filterVariant: "dateRange" },
		},
		{
			accessorKey: "effectiveLeaveDays",
			header: ({ column }) => (
				<ColumnHeader
					sx={{ minWidth: 200 }}
					column={column}
					title="Total Hari Kerja"
				/>
			),
			cell: (info) => `${info.getValue()} hari`,
			enableColumnFilter: false,
			enableSorting: false,
			meta: { columnLabel: "Total Hari Kerja" },
		},
		{
			accessorKey: "reviewerName",
			header: ({ column }) => <ColumnHeader column={column} title="Reviewer" />,
			cell: (info) => {
				const value = info.getValue();

				if (value === null) {
					return "-";
				}

				return value;
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Reviewer", filterVariant: "textSearch" },
		},
		{
			accessorKey: "reviewerEmail",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Reviewer Email" />
			),
			cell: (info) => {
				const value = info.getValue();

				if (value === null) {
					return "-";
				}

				return value;
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Reviewer Email", filterVariant: "textSearch" },
		},
		{
			accessorKey: "createdAt",
			header: ({ column }) => <ColumnHeader column={column} title="Dibuat" />,
			cell: (info) => {
				const value = info.getValue() as string;
				return new Date(value).toLocaleDateString("id-ID");
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Dibuat", filterVariant: "dateRange" },
		},
	];

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				Permintaan Cuti
			</Typography>
			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflow: "auto",
				}}
			>
				<DataTable
					columns={columns}
					fetchData={getAllLeaveRequest}
					enableSelection
					onSelectionChange={setSelectionData}
					renderRowActions={(row) => (
						<RowActions
							row={row}
							onEdit={(data: LeaveRequestResponse) =>
								navigate(`/leaves/${data.id}/edit`)
							}
							viewTitle="Detail Permintaan Cuti"
							renderDetail={(data: LeaveRequestResponse) => (
								<>
									<Stack direction="column" spacing={2} sx={{ mt: 1 }}>
										<Stack
											direction="column"
											spacing={2}
											sx={{ mt: 1, textAlign: "right" }}
										>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Nama Karyawan
												</Typography>
												<Typography>{data.userName}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Email Karyawan
												</Typography>
												<Typography>{data.userEmail}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Kebijakan Cuti
												</Typography>
												<Typography>{data.name}</Typography>
											</Stack>
											<Stack direction="column" textAlign="left">
												<Typography color="textDisabled">Deskripsi</Typography>
												<Typography>{data.reason}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Status</Typography>
												<Box>
													<Chip
														label={
															LeaveRequestStatusLabel[
																data.status as keyof typeof LeaveRequestStatusLabel
															]
														}
														color={
															data.status === LeaveRequestStatus.PENDING
																? "warning"
																: data.status === LeaveRequestStatus.APPROVED
																	? "success"
																	: "error"
														}
														size="small"
													/>
												</Box>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Tanggal Mulai
												</Typography>
												<Typography>{data.startDate}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Tanggal Selesai
												</Typography>
												<Typography>{data.endDate}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Total Hari</Typography>
												<Typography>{data.effectiveLeaveDays} hari</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Dokumen</Typography>
												<Typography>
													{data.documentUrl ? (
														<a
															href={data.documentUrl}
															target="_blank"
															rel="noopener noreferrer"
															style={{
																color: "inherit",
																textDecoration: "underline",
															}}
														>
															Lihat Dokumen
														</a>
													) : (
														"-"
													)}
												</Typography>
											</Stack>
										</Stack>
									</Stack>
								</>
							)}
						/>
					)}
					toolbar={({ hideableColumns, onDeselectAll }) => {
						return (
							<Stack
								direction="row"
								alignItems="center"
								justifyContent="space-between"
								sx={{ mb: 4 }}
							>
								<Box>
									<Typography variant="subtitle1">
										Tabel Manajemen Permintaan Cuti
									</Typography>
								</Box>
								<Stack direction="row" spacing={2} alignItems="center">
									<Button
										component={ReactRouterLink}
										to="/leaves/new"
										variant="contained"
										color="primary"
										startIcon={<IconWrapper icon={IconsaxAddSquareIcon} />}
									>
										Tambah Data
									</Button>
									<Button
										variant="outlined"
										color="inherit"
										startIcon={<IconWrapper icon={IconsaxReceiveSquareIcon} />}
										onClick={() => handleBulkAction("export", onDeselectAll)}
									>
										Export Data
									</Button>
									<ViewOptions hideableColumns={hideableColumns} />
								</Stack>
							</Stack>
						);
					}}
				/>
			</Box>

			<Box
				sx={{
					position: "fixed",
					bottom: 24,
					right: 24,
					zIndex: 1000,
				}}
			>
				<Button
					component={ReactRouterLink}
					to="/leave/create"
					variant="contained"
					color="primary"
					sx={{
						borderRadius: "50%",
						minWidth: 56,
						width: 56,
						height: 56,
						boxShadow: 3,
					}}
				>
					<IconWrapper icon={IconsaxAddSquareIcon} />
				</Button>
			</Box>
			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};

export default LeaveRequestListPage;
