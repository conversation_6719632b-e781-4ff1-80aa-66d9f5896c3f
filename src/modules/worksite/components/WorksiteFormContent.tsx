import { <PERSON>, Button, Grid, Stack, Typography } from "@mui/material";
import { useFormContext } from "react-hook-form";
import { Link as ReactRouterLink } from "react-router";
import { Footer } from "@/shared/components/common/Footer";
import { AutocompleteSelectInput } from "@/shared/components/common/Form/AutoCompleteSelectInput";
import { MapField } from "@/shared/components/common/Form/MapField";
import { TextInput } from "@/shared/components/common/Form/TextInput";
import { TimeInput } from "@/shared/components/common/Form/TimeInput";
import { ButtonSkeleton } from "@/shared/components/common/Skeleton/ButtonSkeleton";
import { timezoneSelectOptions, worksiteTypeSelectOptions } from "../constants";

export const WorksiteFormContent: React.FC<{
	worksiteId?: string;
	isLoading: boolean;
	isSubmitting: boolean;
	label: string;
}> = ({ worksiteId, isLoading, isSubmitting, label }) => {
	const { watch, setValue } = useFormContext();
	const lat = watch("latitude");
	const lng = watch("longitude");
	const radius = watch("radiusInMeter");

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				{label}
			</Typography>

			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflowX: "auto",
				}}
			>
				{/* Worksite Info Section */}
				<Stack
					direction="row"
					alignItems="center"
					justifyContent="space-between"
				>
					<Typography
						variant="body1"
						sx={{ fontWeight: 700, fontSize: "1.25rem" }}
					>
						Informasi Lokasi Kerja
					</Typography>
				</Stack>

				<Box sx={{ mt: 4 }}>
					<Grid container spacing={2}>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="name"
								label="Nama Lokasi"
								placeholder="Masukkan nama lokasi kerja"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<AutocompleteSelectInput
								name="type"
								label="Tipe Lokasi"
								placeholder="Pilih tipe lokasi"
								options={worksiteTypeSelectOptions}
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="address"
								label="Alamat"
								placeholder="Masukkan alamat lengkap"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 8 }}>
							<TextInput
								name="description"
								label="Deskripsi"
								placeholder="Masukkan deskripsi lokasi (opsional)"
								multiline
								rows={3}
								isLoading={isLoading}
							/>
						</Grid>
					</Grid>
				</Box>

				{!worksiteId && (
					<>
						{/* Attendance Rules Section */}
						<Box sx={{ mt: 4 }}>
							<Typography
								variant="body1"
								sx={{ fontWeight: 700, fontSize: "1.25rem" }}
							>
								Aturan Absensi
							</Typography>

							<Box sx={{ mt: 4 }}>
								<Grid container spacing={2}>
									{/* Location Settings */}
									<Grid size={{ xs: 12 }}>
										<Typography
											variant="body2"
											sx={{ fontWeight: 600, mb: 2, color: "text.secondary" }}
										>
											Pengaturan Lokasi
										</Typography>
									</Grid>
									<Grid container size={{ xs: 12 }}>
										<Grid size={{ xs: 12, md: 6 }}>
											<Stack direction="column" spacing={2}>
												<TextInput
													type="number"
													name="latitude"
													label="Latitude"
													placeholder="Masukkan latitude"
													isLoading={isLoading}
												/>
												<TextInput
													type="number"
													name="longitude"
													label="Longitude"
													placeholder="Masukkan longitude"
													isLoading={isLoading}
												/>
												<TextInput
													type="number"
													name="radiusInMeter"
													label="Radius (meter)"
													placeholder="Radius"
													isLoading={isLoading}
												/>
												<AutocompleteSelectInput
													name="timezone"
													label="Zona Waktu"
													placeholder="Pilih zona waktu"
													options={timezoneSelectOptions}
													isLoading={isLoading}
												/>
											</Stack>
										</Grid>
										<Grid size={{ xs: 12, md: 6 }}>
											<Box>
												<MapField
													latitude={lat}
													longitude={lng}
													setLatitude={(val) => setValue("latitude", val)}
													setLongitude={(val) => setValue("longitude", val)}
													radius={radius}
												/>
											</Box>
										</Grid>
									</Grid>
								</Grid>
							</Box>
						</Box>

						{/* Time Rules Section */}
						<Box sx={{ mt: 4 }}>
							<Grid container spacing={2}>
								{/* Check-in Rules */}
								<Grid size={{ xs: 12 }}>
									<Typography
										variant="body2"
										sx={{ fontWeight: 600, mb: 2, color: "text.secondary" }}
									>
										Aturan Check-in
									</Typography>
								</Grid>
								<Grid size={{ xs: 12, md: 4, lg: 3 }}>
									<TimeInput
										name="checkInStartTime"
										label="Waktu Mulai Check-in"
										isLoading={isLoading}
									/>
								</Grid>
								<Grid size={{ xs: 12, md: 4, lg: 3 }}>
									<TimeInput
										name="checkInEndTime"
										label="Waktu Akhir Check-in"
										isLoading={isLoading}
									/>
								</Grid>
								<Grid size={{ xs: 12, md: 4, lg: 3 }}>
									<TextInput
										type="number"
										name="checkInToleranceMinutes"
										label="Toleransi (menit)"
										placeholder="Toleransi menit"
										isLoading={isLoading}
									/>
								</Grid>

								{/* Check-out Rules */}
								<Grid size={{ xs: 12 }}>
									<Typography
										variant="body2"
										sx={{
											fontWeight: 600,
											mb: 2,
											mt: 3,
											color: "text.secondary",
										}}
									>
										Aturan Check-out
									</Typography>
								</Grid>
								<Grid size={{ xs: 12, md: 4, lg: 3 }}>
									<TimeInput
										name="checkOutStartTime"
										label="Waktu Mulai Check-out"
										isLoading={isLoading}
									/>
								</Grid>
								<Grid size={{ xs: 12, md: 4, lg: 3 }}>
									<TimeInput
										name="checkOutEndTime"
										label="Waktu Akhir Check-out"
										isLoading={isLoading}
									/>
								</Grid>
								<Grid size={{ xs: 12, md: 4, lg: 3 }}>
									<TextInput
										type="number"
										name="checkOutToleranceMinutes"
										label="Toleransi (menit)"
										placeholder="Toleransi menit"
										isLoading={isLoading}
									/>
								</Grid>

								{/* Break Rules */}
								<Grid size={{ xs: 12 }}>
									<Typography
										variant="body2"
										sx={{
											fontWeight: 600,
											mb: 2,
											mt: 3,
											color: "text.secondary",
										}}
									>
										Aturan Istirahat
									</Typography>
								</Grid>
								<Grid size={{ xs: 12, md: 4, lg: 3 }}>
									<TimeInput
										name="breakStartTime"
										label="Waktu Mulai Istirahat"
										isLoading={isLoading}
									/>
								</Grid>
								<Grid size={{ xs: 12, md: 4, lg: 3 }}>
									<TimeInput
										name="breakEndTime"
										label="Waktu Akhir Istirahat"
										isLoading={isLoading}
									/>
								</Grid>
								<Grid size={{ xs: 12, md: 4, lg: 3 }}>
									<TextInput
										type="number"
										name="breakToleranceMinutes"
										label="Toleransi (menit)"
										placeholder="Toleransi menit"
										isLoading={isLoading}
									/>
								</Grid>

								{/* Return Rules */}
								<Grid size={{ xs: 12 }}>
									<Typography
										variant="body2"
										sx={{
											fontWeight: 600,
											mb: 2,
											mt: 3,
											color: "text.secondary",
										}}
									>
										Aturan Kembali dari Istirahat
									</Typography>
								</Grid>
								<Grid size={{ xs: 12, md: 4, lg: 3 }}>
									<TimeInput
										name="returnStartTime"
										label="Waktu Mulai Kembali"
										isLoading={isLoading}
									/>
								</Grid>
								<Grid size={{ xs: 12, md: 4, lg: 3 }}>
									<TimeInput
										name="returnEndTime"
										label="Waktu Akhir Kembali"
										isLoading={isLoading}
									/>
								</Grid>
								<Grid size={{ xs: 12, md: 4, lg: 3 }}>
									<TextInput
										type="number"
										name="returnToleranceMinutes"
										label="Toleransi (menit)"
										placeholder="Toleransi menit"
										isLoading={isLoading}
									/>
								</Grid>
							</Grid>
						</Box>
					</>
				)}

				<Stack
					direction="row"
					spacing={2}
					alignItems="center"
					justifyContent="end"
					sx={{ mt: 4 }}
				>
					{isLoading ? (
						<>
							<ButtonSkeleton size="medium" />
							<ButtonSkeleton size="medium" />
						</>
					) : (
						<>
							<Button
								component={ReactRouterLink}
								to="/worksites"
								variant="contained"
								color="error"
								sx={{ px: 3 }}
								disabled={isSubmitting}
							>
								Batal
							</Button>
							<Button
								type="submit"
								variant="contained"
								color="primary"
								sx={{ px: 3 }}
								loading={isSubmitting}
								loadingPosition="start"
							>
								Simpan
							</Button>
						</>
					)}
				</Stack>
			</Box>

			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};
