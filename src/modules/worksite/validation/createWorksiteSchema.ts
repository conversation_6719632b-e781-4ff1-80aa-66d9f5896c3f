import z from "zod";
import { INDONESIA_TIMEZONES } from "../constants";

export const createWorksiteSchema = z.object({
	// data for worksite
	name: z.string().nonempty({ error: "Nama lokasi kerja wajib diisi" }),
	type: z.string().nonempty({ error: "Tipe lokasi kerja wajib diisi" }),
	address: z.string().nonempty({ error: "Alamat lokasi kerja wajib diisi" }),
	description: z.string().nonempty().optional(),

	// data for attendance rule
	latitude: z.number({ error: "Wajib mengisi latitude" }).min(-90).max(90),
	longitude: z.number({ error: "Wajib mengisi longitude" }).min(-180).max(180),
	radiusInMeter: z.number({ error: "Wajib mengisi radius" }).min(10),
	timezone: z.enum(INDONESIA_TIMEZONES, { error: "Wajib memilih zona waktu" }),
	checkInStartTime: z.iso.time(),
	checkInEndTime: z.iso.time(),
	checkInToleranceMinutes: z.number({ error: "Wajib diisi" }).min(0),
	checkOutStartTime: z.iso.time(),
	checkOutEndTime: z.iso.time(),
	checkOutToleranceMinutes: z.number({ error: "Wajib diisi" }).min(0),
	breakStartTime: z.iso.time(),
	breakEndTime: z.iso.time(),
	breakToleranceMinutes: z.number({ error: "Wajib diisi" }).min(0),
	returnStartTime: z.iso.time(),
	returnEndTime: z.iso.time(),
	returnToleranceMinutes: z.number({ error: "Wajib diisi" }).min(0),
});
export type CreateWorksiteSchema = z.infer<typeof createWorksiteSchema>;
